# === 前置导入模块 ===
import os
import json
import traceback
import smtplib
from datetime import datetime, timedelta
from collections import Counter
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

CONFIG_FILE = "us_2days_config.json"
EXPORT_FILE_PREFIX = "us_strategy_result"
HTML_FILE_PREFIX = "us_strategy_report"
RANK_FILE = "us_rank_history.json"
MINUTE_DATA_DIR = r"D:\dev\limuguwang\py\ak_quant\data\US\5_mins"
MAX_WORKERS = min(20, (os.cpu_count() or 4) * 5)

def log(msg):
    print(f"[LOG] {msg}")

def list_all_stocks():
    stocks = []
    if not os.path.exists(MINUTE_DATA_DIR):
        log(f"❌ 本地分钟数据目录不存在: {MINUTE_DATA_DIR}")
        return pd.DataFrame()
    for filename in os.listdir(MINUTE_DATA_DIR):
        if filename.endswith(".csv"):
            code = filename.replace("_5_mins", "").replace(".csv", "")
            stocks.append({"代码": code, "名称": code})
    log(f"共发现股票: {len(stocks)}")
    return pd.DataFrame(stocks)

def get_minute_data(symbol, trade_date):
    path = os.path.join(MINUTE_DATA_DIR, f"{symbol}_5_mins.csv")
    if not os.path.exists(path):
        return pd.DataFrame()
    try:
        df = pd.read_csv(path, parse_dates=['date'])
        if df['date'].dt.tz is not None:
            df['date'] = df['date'].dt.tz_localize(None)
        df = df[df['date'].dt.date == trade_date]
        df = df.sort_values('date')
        return df
    except Exception as e:
        log(f"❌ 分钟数据读取失败 {symbol}: {e}")
        return pd.DataFrame()

def simulate_intraday_trade(minute_df, buy_price, take_profit, stop_loss):
    tp_price = round(buy_price * (1 + take_profit), 2)
    sl_price = round(buy_price * (1 - stop_loss), 2)
    for _, row in minute_df.iterrows():
        if row['high'] >= tp_price:
            return tp_price, "成功", row['date']
        elif row['low'] <= sl_price:
            return sl_price, "止损卖出", row['date']
    return minute_df.iloc[-1]['close'], "收盘卖出", minute_df.iloc[-1]['date']

def get_daily_data(symbol, start_date):
    try:
        path = os.path.join(MINUTE_DATA_DIR, f"{symbol}_5_mins.csv")
        if not os.path.exists(path):
            return pd.DataFrame()
        df = pd.read_csv(path, parse_dates=['date'])
        if df['date'].dt.tz is not None:
            df['date'] = df['date'].dt.tz_localize(None)
        df = df[df['date'] >= pd.to_datetime(start_date)]
        df['day'] = df['date'].dt.date
        daily = df.groupby('day').agg({
            'open': 'first', 'high': 'max', 'low': 'min',
            'close': 'last', 'volume': 'sum'
        }).reset_index().rename(columns={'day': 'date'})
        daily['date'] = pd.to_datetime(daily['date'])
        return daily.sort_values('date').reset_index(drop=True)
    except Exception as e:
        log(f"读取日线失败 {symbol}: {e}")
        return pd.DataFrame()

def backtest(df, symbol, buy_offset, take_profit, stop_loss, min_volume, max_insufficient_days):
    trades, total_ret, valid_total, wins, insufficient_days = [], 0, 0, 0, 0
    df = df.sort_values("date")
    today = datetime.now().date()
    df = df[df['date'].dt.date <= today]
    for i in range(1, len(df)):
        prev, curr = df.iloc[i - 1], df.iloc[i]
        buy_price = round(prev['close'] * (1 + buy_offset), 2)
        trade_date = pd.to_datetime(curr['date']).date()
        volume = curr['volume']
        if volume < min_volume:
            insufficient_days += 1
            trades.append({"买入时间": prev['date'], "卖出时间": curr['date'], "结果": "成交量不足", "成交量": volume})
            continue
        valid_total += 1
        minute_df = get_minute_data(symbol, trade_date)
        if minute_df.empty:
            sell_price, result, ts = curr['close'], "收盘卖出", curr['date']
        else:
            sell_price, result, ts = simulate_intraday_trade(minute_df, buy_price, take_profit, stop_loss)
        try:
            if not np.isfinite(buy_price) or buy_price == 0:
                ret_pct = 0
            else:
                ret_pct = round((sell_price / buy_price - 1) * 100, 2)
        except ZeroDivisionError:
            ret_pct = 0
        trades.append({
            "买入时间": prev['date'], "卖出时间": ts, "买入价格": buy_price,
            "卖出价格": sell_price, "收益率%": ret_pct, "结果": result, "成交量": volume
        })
        total_ret += ret_pct
        if ret_pct > 0:
            wins += 1
    if insufficient_days > max_insufficient_days:
        return None, [], 0, 0.0, 0
    recent_30_trades = [t for t in trades if pd.to_datetime(t["卖出时间"]).date() >= today - timedelta(days=30)]
    return wins / valid_total if valid_total else 0, recent_30_trades, valid_total, total_ret, valid_total

def run_backtest(row, strat):
    symbol, name = row['代码'], row['名称']
    df = get_daily_data(symbol, strat.get('start_date', '2020-01-01'))
    if df.empty or len(df) < strat['min_trade_days']:
        return None
    sr, trades, count, total_ret, valid_total = backtest(
        df, symbol, strat['buy_offset'], strat['take_profit'], strat['stop_loss'],
        strat['min_volume'], strat.get('max_insufficient_volume_days', 5)
    )
    if sr is None or count < strat['min_trade_days'] or sr < strat['success_rate_threshold']:
        return None
    counter = Counter([t["结果"] for t in trades])
    recent_ret = round(sum([t["收益率%"] for t in trades if "收益率%" in t]) or 0, 2)
    return {
        "代码": symbol, "名称": name,
        "成功率": round(sr * 100, 2),
        "累计收益率%": round(total_ret, 2),
        "近30日收益%": recent_ret,
        "交易次数": count,
        "成功次数": counter.get("成功", 0),
        "止损次数": counter.get("止损卖出", 0),
        "收盘卖出次数": counter.get("收盘卖出", 0),
        "成交量不足": counter.get("成交量不足", 0),
        "交易记录": trades
    }

def update_rankings(results, strategy_name):
    today = datetime.now().strftime("%Y-%m-%d")
    rank_data = {}
    if os.path.exists(RANK_FILE):
        rank_data = json.load(open(RANK_FILE, "r", encoding="utf-8"))
    for i, r in enumerate(results):
        code = r['代码']
        data = rank_data.get(code, {})
        rank = i + 1
        last_rank = data.get("current_rank", rank)
        data.update({
            "名称": r['名称'], "last_rank": last_rank, "current_rank": rank,
            "days_in_top": data.get("days_in_top", 0) + 1 if last_rank == rank - 1 else 1,
            "last_update": today, "success_rate": r["成功率"], "近30日收益%": r.get("近30日收益%", 0)
        })
        rank_data[code] = data
    rank_data = {k: v for k, v in rank_data.items() if k in [r["代码"] for r in results]}
    json.dump(rank_data, open(RANK_FILE, "w", encoding="utf-8"), ensure_ascii=False, indent=2)
    return pd.DataFrame([
        {
            "代码": k, "名称": v["名称"], "成功率%": v["success_rate"],
            "当前排名": v["current_rank"],
            "上次排名": v.get("last_rank", "-"),
            "排名变化": (v.get("last_rank", 0) or v["current_rank"]) - v["current_rank"],
            "在榜天数": v["days_in_top"],
            "近30日收益%": v.get("近30日收益%", 0)
        } for k, v in rank_data.items()
    ]).sort_values("当前排名")

def generate_html(results, title, strat, rank_df):
    html = f"<html><head><meta charset='utf-8'><style>table{{border-collapse:collapse;width:100%;}}th,td{{border:1px solid #ccc;padding:6px;text-align:center;}}</style></head><body>"
    html += f"<h2>策略报告：{title}</h2>"
    html += f"<p>策略参数：买入价偏移：{round(strat['buy_offset']*100,2)}% | 止盈：{round(strat['take_profit']*100,2)}% | 止损：{round(strat['stop_loss']*100,2)}%</p>"
    html += "<h3>股票排行榜</h3>"
    html += rank_df.to_html(index=False)
    for r in results:
        html += f"<h3>{r['代码']} - {r['名称']} | 成功率：{r['成功率']}% | 累计收益：{r['累计收益率%']}%</h3>"
        html += f"<p>交易次数：{r['交易次数']} | 成功：{r['成功次数']} | 止损：{r['止损次数']} | 收盘卖出：{r['收盘卖出次数']} | 成交量不足：{r['成交量不足']}</p>"
        html += pd.DataFrame(r['交易记录']).to_html(index=False)
        html += "<hr>"
    html += "</body></html>"
    return html

def send_email(subject, html_content, to_addr, from_addr, smtp_server, smtp_port, smtp_user, smtp_pass):
    msg = MIMEMultipart("alternative")
    msg["Subject"], msg["From"], msg["To"] = subject, from_addr, to_addr
    msg.attach(MIMEText(html_content, "html", "utf-8"))
    try:
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(smtp_user, smtp_pass)
        server.sendmail(from_addr, [to_addr], msg.as_string())
        server.quit()
        log("✅ 邮件发送成功")
    except Exception as e:
        log(f"❌ 邮件发送失败: {e}")

def main():
    with open(CONFIG_FILE, "r", encoding="utf-8") as f:
        cfg = json.load(f)
    stock_list = list_all_stocks()
    email_cfg = cfg.get("email", {})
    for strat in cfg.get("strategies", []):
        log(f"▶ 执行策略：{strat['name']}")
        results = []
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as pool:
            futures = {pool.submit(run_backtest, row, strat): row["代码"] for _, row in stock_list.iterrows()}
            for f in as_completed(futures):
                r = f.result()
                if r:
                      results.append(r)
                log(f"✅ {r['代码']} - {r['名称']} | 成功率: {r['成功率']}% | 累计收益: {r['累计收益率%']}% | 交易次数: {r['交易次数']}")

                if r.get("未达标"):
                  log(f"⚠️ {r['代码']} 未达标，成功率: {r['成功率']}% < 阈值: {strat['success_rate_threshold']*100}%")
        results = sorted(results, key=lambda x: (x['成功率'], x['累计收益率%']), reverse=True)[:strat['top_n']]
        if not results:
            log("❌ 无符合策略结果")
            continue
        pd.concat([pd.DataFrame(r['交易记录']) for r in results]).to_excel(f"{EXPORT_FILE_PREFIX}_{strat['name']}.xlsx", index=False)
        rank_df = update_rankings(results, strat['name'])
        rank_df.to_excel(f"{EXPORT_FILE_PREFIX}_{strat['name']}_rank.xlsx", index=False)
        html = generate_html(results, strat['name'], strat, rank_df)
        html_path = f"{HTML_FILE_PREFIX}_{strat['name']}.html"
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html)
        send_email(f"{email_cfg.get('subject_prefix', '美股策略报告')} - {strat['name']}",
                   html, email_cfg['to'], email_cfg['from'], email_cfg['smtp_server'],
                   email_cfg['smtp_port'], email_cfg['smtp_user'], email_cfg['smtp_password'])

if __name__ == "__main__":
    main()
